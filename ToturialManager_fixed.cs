using System.Collections;
using DG.Tweening;
using UnityEngine;

public class ToturialManager : Node
{
    [Header("Character")]
    [SerializeField] private BaseCharacter teacher;
    [SerializeField] private BaseCharacter boy;
    [SerializeField] private BaseCharacter girl;
    
    [SerializeField] private Transform teacherTargetTransform;
    [SerializeField] private Transform boyTargetTransform;
    [SerializeField] private Transform girlTargetTransform;

    [SerializeField] private Transform helloTransform;
    
    [SerializeField] private Transform teacherInitTransform;
    [SerializeField] private Transform boyInitTransform;
    [SerializeField] private Transform girlInitTransform;

    [Header("Audio")]
    [SerializeField] private AudioClip teacherHelloAudio;
    [SerializeField] private AudioClip boyHelloAudio;
    [SerializeField] private AudioClip girlHelloAudio;
    
    [Header("Timing Settings")]
    [SerializeField] private float teacherMoveToHelloDuration = 3f;
    [SerializeField] private float waitAfterTeacherMove = 4f;
    [SerializeField] private float waitAfterTeacher<PERSON>ello = 1f;
    [SerializeField] private float characterRotationDuration = 0.5f;
    [SerializeField] private float animationTransitionTime = 0.1f;
    [SerializeField] private float idleAnimationOffset = 0.35f;
    
    public bool IsTutorialDone
    {
        get
        {
            return PlayerPrefs.GetInt("Tutorial_Done", 0) == 1;
        }
        set
        {
            if (value)
            {
                PlayerPrefs.SetInt("Tutorial_Done", 1);
            }
        }
    }
    public void Start()
    {
        if (IsTutorialDone)
        {
            CameraManager.Instance.ToturialCamera.Deactivate();
            CameraManager.Instance.MainCamera.Activate();
            Deactivate();
            return;
        }
        Activate();
        CameraManager.Instance.MainCamera.Deactivate();
        CameraManager.Instance.ToturialCamera.Activate();
        StartCoroutine(Toturial());
    }
    
    IEnumerator Toturial()
    {
        StartCoroutine(TeacherHello());
        yield return null;
    }
    
    IEnumerator TeacherHello()
    {
        teacher.transform.position=(teacherInitTransform.position);
        /*teacher.transform.rotation = Quaternion.LookRotation(helloTransform.position - teacher.transform.position, Vector3.up);
        teacher.transform.DOMove(helloTransform.position, 2f);
        teacher.animator.Play("Walk");
        yield return new WaitForSeconds(2f);
        teacher.animator.HarshPlay("Idle");
        teacher.transform.DORotate(helloTransform.rotation.eulerAngles, .5f);*/
        StartCoroutine(MoveToTransform(teacher, helloTransform, teacherMoveToHelloDuration));
        yield return new WaitForSeconds(waitAfterTeacherMove);
        teacher.Hello(teacherHelloAudio, "Xin chào các em, cô tên là Loan");
        yield return new WaitForSeconds(teacherHelloAudio.length + waitAfterTeacherHello);
        StartCoroutine(MoveToTransform(teacher, teacherTargetTransform, teacherMoveToHelloDuration));
    }

    IEnumerator MoveToTransform(BaseCharacter character, Transform targetTransform, float duration)
    {
        var rotation = Quaternion.LookRotation(targetTransform.position - character.transform.position, Vector3.up).eulerAngles;
        character.transform.DORotate(rotation, characterRotationDuration);
        character.transform.DOMove(targetTransform.position, duration);
        character.animator.ImmediatePlay("Walk", animationTransitionTime);
        Sequence(Delay(duration - idleAnimationOffset).OnComplete(() =>
        {
            character.animator.ImmediatePlay("Idle", animationTransitionTime);
        }));
        yield return new WaitForSeconds(duration - idleAnimationOffset);
        character.transform.DORotate(targetTransform.rotation.eulerAngles, characterRotationDuration);
    }
}
