using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class PracticePanel : UIPanel
{
    [SerializeField]
    private List<GameObject> ExercisesGameObjects;
    
    public void OnBackButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.PracticePanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.LessonPanel);
    }
    
    public void OnHomeButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.PracticePanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.LessonIntroPanel);
        UIManager.Instance.BookCurlBack();
    }

    public void OnExerciseButtonClicked(int index)
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.PracticePanel);
        ExercisesGameObjects[index].SetActive(true);
    }

    private void Reset()
    {
        var button= GetComponentsInChildren<Button>();
        for (int i = 0; i < button.Length; i++)
        {
            button[i].GetComponentInChildren<TextMeshProUGUI>().text= "Bài tập " + (i+1);
            button[i].onClick.AddListener(() => OnExerciseButtonClicked(i));
        }
    }
}
