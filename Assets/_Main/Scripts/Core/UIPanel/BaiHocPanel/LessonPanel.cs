using UnityEngine;

public class LessonPanel : UIPanel
{
    public void OnBackButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.LessonPanel);
        UIManager.Instance.BookCurlBack();
    }
    
    public void OnExploreButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.LessonPanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.ExplorationPanel);
    }
    
    public void OnPracticeButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.LessonPanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.PracticePanel);
    }
}
