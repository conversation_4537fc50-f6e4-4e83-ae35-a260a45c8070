using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ExplorePanel : UIPanel
{
    [SerializeField]
    private List<GameObject> ExplorationGameObjects;
    
    public void OnBackButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.ExplorationPanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.LessonPanel);
    }

    public void OnHomeButtonClicked()
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.ExplorationPanel);
        UIManager.Instance.ShowPanel(UIManager.PanelType.LessonIntroPanel);
        UIManager.Instance.BookCurlBack();
    }
    public void OnExerciseButtonClicked(int index)
    {
        UIManager.Instance.HidePanel(UIManager.PanelType.ExplorationPanel);
        ExplorationGameObjects[index].SetActive(true);
    }

    private void Reset()
    {
        Debug.Log("aaaa");
        var buttons= GetComponentsInChildren<Button>();
        /*Debug.Log($"Tìm thấy {buttons.Length} buttons");*/
        for (int i = 0; i < buttons.Length; i++)
        {
            if(i< ExplorationGameObjects.Count)
            {
                int capturedIndex = i;
                buttons[i].gameObject.SetActive(true);
                buttons[i].onClick.RemoveAllListeners();
                buttons[i].onClick.AddListener(() => OnExerciseButtonClicked(capturedIndex));
                
                // Đặt tên và text nếu cần
                buttons[i].name = "HoatDong" + (i+1);
                TextMeshProUGUI text = buttons[i].GetComponentInChildren<TextMeshProUGUI>();
                if (text != null)
                {
                    text.text = "Hoạt động " + (i+1);
                }
            }
            else
            {
                buttons[i].gameObject.SetActive(false);
            }
        }
    }
}
