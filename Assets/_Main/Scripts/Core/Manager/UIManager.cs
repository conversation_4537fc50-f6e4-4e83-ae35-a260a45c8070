using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class UIManager : Singleton<UIManager>
{
    [Header("Panels")]
    [SerializeField]
    private LessonPanel lessonPanel;
    [SerializeField]
    private LessonIntroPanel lessonIntroPanel;
    [SerializeField]
    private PracticePanel practicePanel;
    [SerializeField]
    private CompletePanel completePanel;
    [SerializeField]
    private NumberInputPanel numberInputPanel;
    [SerializeField]
    private ConfirmSubmitPanel confirmSubmitPanel;
    [SerializeField]
    private ExplorePanel explorePanel;

    [SerializeField] private GameObject blockPanel;
    
    [Header("Buttons")]
    [SerializeField]
    private GameObject zoomButton;
    [SerializeField]
    private GameObject submitButton;
    [SerializeField]
    private GameObject completeButton;
    
    public enum PanelType
    {
        MenuPanel,
        LessonIntroPanel,
        LessonPanel,
        ExplorationPanel,
        PracticePanel,
        EvaluationPanel,
        NumberInputPanel,
        CompletePanel,
        BlockPanel,
        ConfirmSubmitPanel
    }
     protected override void Awake()
    {
        base.Awake();
        RegisterEvents();
    }

    private void RegisterEvents()
    {
        MainManager.Instance.OnInteractive += ShowInteractiveButtons;
        MainManager.Instance.OnUnInteractive += HideInteractiveButtons;
    }
    
    private void ShowInteractiveButtons()
    {
        ShowZoomButton();
        ShowCompleteButton();
        if (MainManager.Instance.isQuestStarted)
        {
            blockPanel.SetActive(false);
        }
    }
    
    private void HideInteractiveButtons()
    {
        /*Debug.Log("aaaaaaaaaaa");*/
        HideZoomButton();
        HideCompleteButton();
        if (MainManager.Instance.isQuestStarted)
        {
            blockPanel.SetActive(true);
        }
    }

    public void ShowZoomButton()
    {
        if (zoomButton&& !zoomButton.activeInHierarchy)
        {
            zoomButton.SetActive(true);
        }
    }
    
    public void HideZoomButton()
    {
        Debug.Log("tat zoom");
        if (zoomButton&& zoomButton.activeInHierarchy)
        {
            zoomButton.SetActive(false);
        }
    }
    
    public void ShowCompleteButton()
    {
        if (completeButton&& !completeButton.activeInHierarchy)
        {
            completeButton.SetActive(true);
        }
    }
    
    public void HideCompleteButton()
    {
        if (completeButton&& completeButton.activeInHierarchy)
        {
            completeButton.SetActive(false);
        }
    }

    public void ShowPanel(PanelType panelType)
    {
        switch (panelType)
        {
            case PanelType.LessonPanel:
                /*lessonPanel.SlideDown(lessonPanel.GetRectTransform(),0,Constants.SlideDownTime);*/
                lessonPanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.LessonIntroPanel:
                lessonIntroPanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.PracticePanel:
                practicePanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.NumberInputPanel:
                numberInputPanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.ConfirmSubmitPanel:
                confirmSubmitPanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.ExplorationPanel:
                explorePanel.Open(Constants.OpenPanelTime);
                break;
            case PanelType.BlockPanel:
                blockPanel.SetActive(true);
                break;
            default:
                /*Debug.LogError("UIManager: ShowBaiHocPanel: Invalid panel type");*/
                break;
        }
    }

    public void HidePanel(PanelType panelType)
    {
        switch (panelType)
        {
            case PanelType.LessonPanel:
                lessonPanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.LessonIntroPanel:
                lessonIntroPanel.Deactivate();
                break;
            case PanelType.PracticePanel:
                practicePanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.CompletePanel:
                completePanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.NumberInputPanel:
                numberInputPanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.ConfirmSubmitPanel:
                confirmSubmitPanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.ExplorationPanel:
                explorePanel.Close(Constants.ClosePanelTime);
                break;
            case PanelType.BlockPanel:
                blockPanel.SetActive(false);
                break;
            default:
                /*Debug.LogError("UIManager: HideBaiHocPanel: Invalid panel type");*/
                break;
        }
    }

    public void BookCurlBack()
    {
        StartCoroutine(CurlBack());
    }

    private IEnumerator CurlBack()
    {
        lessonIntroPanel.Activate();
        yield return new WaitUntil(() => lessonIntroPanel.gameObject.activeInHierarchy);
        lessonIntroPanel.FlipLeftPage();
    }

    public void ShowCompletePanel(int correctCount, int wrongCount, int mistakeCount, float time)
    {
        completePanel.SetResult(correctCount, wrongCount, mistakeCount, time);
        completePanel.Open(Constants.OpenPanelTime);
    }
    public NumberInputPanel GetNumberInputPanel()
    {
        return numberInputPanel;
    }
    
    public Button GetSubmitButton()
    {
        return submitButton.GetComponent<Button>();
    }

    public void OnCompleteButtonClicked()
    {
        if (MainManager.Instance.isQuestStarted)
        {
            confirmSubmitPanel.Open(Constants.OpenPanelTime);
        }
    }
}
