using System;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine.Events;

public class BaiTap_1 : Node
{
    [Header("<PERSON><PERSON><PERSON> hình bài tập")]
    public string evaluationCode = "T2Snd1";
    [SerializeField] private List<GameObject> grids; // Danh sách hình lưới
    [SerializeField] private List<GameObject> numbers; // Danh sách số
    [SerializeField] private float lineWidth = 0.1f; // Độ rộng đường nối
    [SerializeField] private Color defaultLineColor = UnityEngine.Color.gray; // Màu mặc định khi nối
    [SerializeField] private Color correctLineColor = UnityEngine.Color.green; // Màu đường nối đúng
    [SerializeField] private Color wrongLineColor = UnityEngine.Color.red; // Màu đường nối sai
    [SerializeField] private Button completeButton; // Nút "Hoàn thành"
    [SerializeField] private Text warningText; // Text hiển thị cảnh báo

    [Header("Events")]
    public UnityEvent onExerciseCompleted; // Event khi hoàn thành bài tập
    public UnityEvent onCorrectAnswer; // Event khi nối đúng
    public UnityEvent onWrongAnswer; // Event khi nối sai

    [SerializeField]
    private AudioClip teacherToturialAudio;
    // Dictionary lưu cặp đúng (hình -> số)
    private Dictionary<GameObject, GameObject> _correctPairs;
    // Dictionary lưu cặp người dùng đã nối (hình -> số)
    private Dictionary<GameObject, GameObject> _userConnections = new Dictionary<GameObject, GameObject>();
    // Dictionary lưu đường nối (hình -> ConnectionLine)
    private Dictionary<GameObject, ConnectionLine> _connectionLines = new Dictionary<GameObject, ConnectionLine>();

    // Đối tượng bắt đầu nối và trạng thái
    private GameObject _startObject;
    private bool _isConnecting = false;
    private bool _hasInitialized = false;

    // Đếm số lần mắc lỗi (khác với số lỗi sai)
    private int _mistakeCount = 0;

    // Object Pooling
    private ObjectPutter _objectPutter;

    void Awake()
    {
        // Lấy reference đến ObjectPutter
        _objectPutter = ObjectPutter.Instance;
        if (_objectPutter == null)
        {
            Debug.LogError("Không tìm thấy ObjectPutter! Hãy đảm bảo đã thêm ObjectPutter vào scene.");
        }

        // Ẩn panel hoàn thành và warningText nếu có
        if (warningText != null) warningText.gameObject.SetActive(false);
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        InitializeExercise();
        completeButton = UIManager.Instance.GetSubmitButton();
        if (completeButton != null)
        {
            completeButton.onClick.AddListener(OnCompleteButtonClicked);
        }
        else
        {
            Debug.LogError("Chưa gán CompleteButton trong Inspector!");
        }
        MainManager.Instance.CanInteractive = true;
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        CleanupEvents();
    }

    private void OnDestroy()
    {
        CleanupEvents();
    }

    private void CleanupEvents()
    {
        if (!_hasInitialized) return;

        // Cleanup button first
        if (completeButton != null)
        {
            completeButton.onClick.RemoveListener(OnCompleteButtonClicked);
        }

        // Safely remove character listeners
        RemoveEventListener();
        
        // Clear all other events
        onExerciseCompleted?.RemoveAllListeners();
        onCorrectAnswer?.RemoveAllListeners();
        onWrongAnswer?.RemoveAllListeners();

        _hasInitialized = false;
    }

    private void AddEventListener()
    {
        // Đánh dấu đã khởi tạo
        _hasInitialized = true;

        // Sử dụng weak reference để tránh strong coupling
        var teacher = TeacherCharacter.Instance;
        if (teacher)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(teacher.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(teacher.FalseReaction);
        }

        var boy = BoyCharacter.Instance;
        if (boy)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(boy.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(boy.FalseReaction);
        }

        var girl = GirlCharacter.Instance;
        if (girl)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(girl.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(girl.FalseReaction);
        }
    }

    private void RemoveEventListener()
    {
        if (!_hasInitialized) return;

        // Cache references trước khi remove
        var teacher = TeacherCharacter.Instance;
        var boy = BoyCharacter.Instance;
        var girl = GirlCharacter.Instance;

        if (teacher != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(teacher.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(teacher.FalseReaction);
        }

        if (boy != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(boy.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(boy.FalseReaction);
        }

        if (girl != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(girl.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(girl.FalseReaction);
        }
    }

    private void InitializeExercise()
    {
        UIManager.Instance.ShowPanel(UIManager.PanelType.BlockPanel);
        AddEventListener();
        /*UIManager.Instance.ShowPanel(UIManager.PanelType.BlockPanel);*/
        /*yield return new WaitUntil(() => TeacherCharacter.Instance);*/
        if (gameObject.name.Equals("BaiTap_1"))
        {
            TeacherCharacter.Instance.Speak(teacherToturialAudio, "Em nối mỗi hình với số thích hợp");
        }else if (gameObject.name.Equals("BaiTap_3"))
        {
            TeacherCharacter.Instance.Speak(teacherToturialAudio, "Em nối mỗi số với lời đọc thích hợp");
        }
        Sequence(Delay(teacherToturialAudio.length+1).OnComplete(() =>
        {
            MainManager.Instance.OnStartQuest?.Invoke();
            TimeManager.Instance.StartTiming();
        }));
        ClearAllConnections();
        _mistakeCount = 0; // Reset số lần mắc lỗi

        if (grids.Count != numbers.Count)
        {
            /*Debug.LogError("Số lượng grids và numbers không bằng nhau! Grids: " + grids.Count + ", Numbers: " + numbers.Count);*/
            return;
        }

        // Khởi tạo cặp đúng
        _correctPairs = new Dictionary<GameObject, GameObject>();
        for (int i = 0; i < grids.Count; i++)
        {
            if (i < numbers.Count)
            {
                _correctPairs.Add(grids[i], numbers[i]);
            }
        }
    }

    // Hàm xử lý khi người dùng nhấn vào đối tượng (Grid hoặc Number)
    public void OnObjectClicked(GameObject clickedObject)
    {
        if (!_isConnecting) // Chưa bắt đầu nối
        {
            // Nếu grid đã được nối, người dùng có thể nhấn để hủy nối
            if (_connectionLines.ContainsKey(clickedObject))
            {
                DisconnectObject(clickedObject);
            }
            else if (grids.Contains(clickedObject))
            {
                _startObject = clickedObject;
                _isConnecting = true;
                HighlightObject(clickedObject, true);
                Debug.Log("Đã chọn: " + clickedObject.name);
            }
        }
        else // Đang nối
        {
            // Nếu nhấn lại vào grid đã chọn để hủy
            if (clickedObject == _startObject)
            {
                _isConnecting = false;
                HighlightObject(_startObject, false);
                _startObject = null;
                return;
            }

            // Nếu click vào số để nối
            if (numbers.Contains(clickedObject))
            {
                // Kiểm tra nếu số này chưa được nối
                if (!_connectionLines.Values.Any(line => line.GetEndObject() == clickedObject))
                {
                    DrawConnectionLine(_startObject, clickedObject);
                    _userConnections[_startObject] = clickedObject;

                    // Kiểm tra ngay sau khi nối
                    bool isCorrect = _correctPairs.ContainsKey(_startObject) && _correctPairs[_startObject] == clickedObject;
                    ConnectionLine line = _connectionLines[_startObject];

                    if (!isCorrect)
                    {
                        // Nối sai: đổi màu đường nối sang màu sai
                        line.SetColor(wrongLineColor);
                        _mistakeCount++; // Tăng số lần mắc lỗi
                        Debug.Log($"Nối sai! Số lần mắc lỗi: {_mistakeCount}");
                        onWrongAnswer?.Invoke();
                    }
                    else
                    {
                        // Nối đúng: đổi màu đường nối sang màu đúng
                        line.SetColor(correctLineColor);
                        Debug.Log("Nối đúng!");
                        onCorrectAnswer?.Invoke();
                    }
                }
                else
                {
                    Debug.Log("Số này đã được nối!");
                }

                HighlightObject(_startObject, false);
                _isConnecting = false;
                _startObject = null;
            }
        }
    }

    // Hủy kết nối đối với một grid đã có kết nối
    private void DisconnectObject(GameObject grid)
    {
        if (_connectionLines.ContainsKey(grid))
        {
            ConnectionLine line = _connectionLines[grid];
            if (line != null)
            {
                line.gameObject.SetActive(false);
            }
            _connectionLines.Remove(grid);
            _userConnections.Remove(grid);
            Debug.Log("Đã hủy kết nối của: " + grid.name);
        }
    }

    // Thay đổi màu highlight đối tượng
    private void HighlightObject(GameObject obj, bool highlight)
    {
        Image image = obj.GetComponent<Image>();
        if (image != null)
        {
            image.color = highlight ? new Color(1f, 1f, 0.8f) : UnityEngine.Color.white;
        }
    }

    // Vẽ đường nối giữa hai đối tượng
    private void DrawConnectionLine(GameObject from, GameObject to)
    {
        Transform lineTransform = _objectPutter.PutObject(SpawnerType.ConnectionLine);
        if (lineTransform != null)
        {
            ConnectionLine connectionLine = lineTransform.GetComponent<ConnectionLine>();
            connectionLine.SetPositions(from.transform.position, to.transform.position);
            connectionLine.SetWidth(lineWidth);
            connectionLine.SetColor(defaultLineColor); // Màu mặc định, sẽ được đánh giá sau
            connectionLine.SetEndObject(to);
            lineTransform.SetParent(transform);
            _connectionLines[from] = connectionLine;
        }
        else
        {
            GameObject lineObj = new GameObject("Line_" + from.name + "_" + to.name);
            lineObj.transform.SetParent(transform);
            ConnectionLine connectionLine = lineObj.AddComponent<ConnectionLine>();
            connectionLine.SetPositions(from.transform.position, to.transform.position);
            connectionLine.SetWidth(lineWidth);
            connectionLine.SetColor(defaultLineColor);
            connectionLine.SetEndObject(to);
            _connectionLines[from] = connectionLine;
        }
    }

    // Xử lý khi nhấn nút "Hoàn thành"
    private void OnCompleteButtonClicked()
    {
        EvaluateExercise();
    }

    // Hiển thị cảnh báo
    private void ShowWarning(string message)
    {
        if (warningText != null)
        {
            warningText.text = message;
            warningText.gameObject.SetActive(true);
            Invoke("HideWarning", 2f);
        }
    }

    private void HideWarning()
    {
        if (warningText != null) warningText.gameObject.SetActive(false);
    }

    // Hàm kiểm tra tất cả các kết nối và đánh giá
    private void EvaluateExercise()
    {
        int errorCount = 0;
        int notConnectedCount = 0;

        // Kiểm tra các cặp đã nối
        foreach (var pair in _userConnections)
        {
            GameObject grid = pair.Key;
            GameObject number = pair.Value;

            if (!(_correctPairs.ContainsKey(grid) && _correctPairs[grid] == number))
            {
                // Nối sai
                errorCount++;
            }
        }

        // Kiểm tra các grid chưa được nối
        foreach (var grid in grids)
        {
            if (!_userConnections.ContainsKey(grid))
            {
                notConnectedCount++;
                errorCount++; // Tính cả các cặp không được nối là sai
            }
        }
        
        onExerciseCompleted?.Invoke();

        float maxScore = grids.Count * 3f; // Giả sử mỗi cặp có 3 điểm tối đa
        float score = maxScore - (errorCount * 0.5f);
        score = Mathf.Clamp(score, 0f, maxScore);
        float timeTaken = TimeManager.Instance.StopTiming();

        SaveResultToDataManager(errorCount, score, timeTaken);

        // Hiển thị thông báo kết quả
        string resultMessage = errorCount > 0
            ? $"Kết quả: {grids.Count - errorCount}/{grids.Count} cặp đúng. Số lần mắc lỗi: {_mistakeCount}. Điểm: {score:F1}/{maxScore:F1}"
            : $"Tuyệt vời! Bạn đã nối đúng tất cả các cặp. Số lần mắc lỗi: {_mistakeCount}. Điểm: {score:F1}/{maxScore:F1}";

        Debug.Log(resultMessage);
        
        Deactivate();
        UIManager.Instance.ShowCompletePanel(grids.Count - errorCount, errorCount, _mistakeCount, timeTaken);
        MainManager.Instance.isQuestStarted = false;
    }

    // Lưu kết quả vào DataManager
    private void SaveResultToDataManager(int errorCount, float score, float timeTaken)
    {
        if (DataManager.Instance != null)
        {
            string metadata = $"{{\"mistakeCount\":{_mistakeCount}}}";
            DataManager.Instance.SaveEvaluationResult("Bài tập 1", evaluationCode, score, timeTaken, errorCount, metadata);
            Debug.Log($"Đã lưu kết quả: Điểm = {score}, Số lỗi = {errorCount}, Số lần mắc lỗi = {_mistakeCount}, Thời gian = {timeTaken:F2}s");
        }
        else
        {
            Debug.LogError("DataManager.Instance không tồn tại!");
        }
    }

    // Xóa tất cả đường nối và reset trạng thái
    private void ClearAllConnections()
    {
        foreach (var connectionLine in _connectionLines.Values)
        {
            if (connectionLine)
            {
                connectionLine.gameObject.SetActive(false);
            }
        }
        _connectionLines.Clear();
        _userConnections.Clear();
        _isConnecting = false;
        _startObject = null;
    }

    // Reset bài tập
    public void ResetExercise()
    {
        ClearAllConnections();
        InitializeExercise();
    }
}
