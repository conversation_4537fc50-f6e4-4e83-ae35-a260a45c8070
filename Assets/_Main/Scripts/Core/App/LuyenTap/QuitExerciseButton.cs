using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

/// <summary>
/// Universal Quit Button for all exercises
/// Handles proper cleanup of tweens, coroutines, and returns to home
/// </summary>
public class QuitExerciseButton : MonoBehaviour
{
    [Header("Button Reference")]
    [SerializeField] private But<PERSON> quitButton;
    
    [<PERSON><PERSON>("Setting<PERSON>")]
    [SerializeField] private bool showConfirmDialog = true;
    [SerializeField] private string confirmMessage = "Bạn có chắc muốn thoát bài tập?";

    private void Awake()
    {
        if (quitButton == null)
        {
            quitButton = GetComponent<Button>();
        }
        
        if (quitButton != null)
        {
            quitButton.onClick.AddListener(OnQuitButtonClicked);
        }
        else
        {
            Debug.LogError("QuitExerciseButton: Không tìm thấy Button component!");
        }
    }

    private void OnDestroy()
    {
        if (quitButton != null)
        {
            quitButton.onClick.RemoveListener(OnQuitButtonClicked);
        }
    }

    public void OnQuitButtonClicked()
    {
        if (showConfirmDialog)
        {
            // TODO: Show confirmation dialog
            // For now, directly quit
            Debug.Log("Quit button clicked - Showing confirmation dialog");
            QuitExercise();
        }
        else
        {
            QuitExercise();
        }
    }

    public void QuitExercise()
    {
        Debug.Log("Quitting exercise - Stopping all tweens and returning to home");
        
        // 1. Stop all coroutines globally
        StopAllCoroutinesGlobally();
        
        // 2. Stop timing if started
        StopTiming();
        
        // 3. Stop character speaking and tweens
        StopCharacterTweens();
        
        // 4. Kill all DOTween animations globally
        DOTween.KillAll(true);
        
        // 5. Reset quest state
        ResetQuestState();
        
        // 6. Return to home
        ReturnToHome();
    }

    private void StopAllCoroutinesGlobally()
    {
        // Find all MonoBehaviour objects and stop their coroutines
        var allMonoBehaviours = FindObjectsOfType<MonoBehaviour>();
        foreach (var mb in allMonoBehaviours)
        {
            if (mb != null)
            {
                mb.StopAllCoroutines();
            }
        }
    }

    private void StopTiming()
    {
        if (TimeManager.Instance != null)
        {
            TimeManager.Instance.StopTiming();
        }
    }

    private void StopCharacterTweens()
    {
        // Stop Teacher Character
        if (TeacherCharacter.Instance != null)
        {
            TeacherCharacter.Instance.StopSpeaking();
            DOTween.Kill(TeacherCharacter.Instance);
        }
        
        // Stop Girl Character  
        if (GirlCharacter.Instance != null)
        {
            GirlCharacter.Instance.StopSpeaking();
            DOTween.Kill(GirlCharacter.Instance);
        }
        
        // Stop Boy Character
        if (BoyCharacter.Instance != null)
        {
            BoyCharacter.Instance.StopSpeaking();
            DOTween.Kill(BoyCharacter.Instance);
        }
    }

    private void ResetQuestState()
    {
        if (MainManager.Instance != null)
        {
            MainManager.Instance.isQuestStarted = false;
        }
    }

    private void ReturnToHome()
    {
        // Hide any open panels
        if (UIManager.Instance != null)
        {
            UIManager.Instance.HidePanel(UIManager.PanelType.BlockPanel);
            UIManager.Instance.HidePanel(UIManager.PanelType.NumberInputPanel);
            UIManager.Instance.HidePanel(UIManager.PanelType.CompletePanel);
            UIManager.Instance.HidePanel(UIManager.PanelType.ConfirmSubmitPanel);
            
            // Show exploration panel (home for exercises)
            UIManager.Instance.ShowPanel(UIManager.PanelType.ExplorationPanel);
        }
        
        // Deactivate current exercise objects
        DeactivateCurrentExercise();
        
        Debug.Log("Successfully returned to home - All tweens stopped");
    }

    private void DeactivateCurrentExercise()
    {
        // Find and deactivate exercise objects
        var exercises = FindObjectsOfType<Node>();
        foreach (var exercise in exercises)
        {
            if (exercise.gameObject.activeInHierarchy && 
                (exercise.name.Contains("BaiTap") || exercise.name.Contains("HoatDong")))
            {
                exercise.Deactivate();
            }
        }
    }
}
