using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

public class BaiTap_9 : Node
{
    [Serializable]
    public class Baitap9Pair
    {
        public int numberLeft;
        public int numberRight;
    }
    
    [SerializeField] private List<Baitap9Pair> pairsnumber;
    [SerializeField] private List<ComparisonPair> comparisonPairs;
    [SerializeField] private AudioClip teacherToturialAudio;
    [SerializeField] private Button completeButton;

    [Header("Events")] public UnityEvent onExerciseCompleted;
    public UnityEvent onCorrectAnswer;
    public UnityEvent onWrongAnswer;
    

    private int _mistakeCount = 0;
    private bool _hasInitialized = false;

    private void Awake()
    {
        completeButton = UIManager.Instance.GetSubmitButton();
        if (completeButton != null)
        {
            completeButton.onClick.AddListener(OnCompleteButtonClicked);
        }
        else
        {
            Debug.LogError("Chưa gán CompleteButton trong Inspector!");
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        ResetComparisonPairs();
        InitComparisonPairs();
        InitializeExercise();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        CleanupEvents();
    }

    private void OnDestroy()
    {
        CleanupEvents();
    }

    private void InitComparisonPairs()
    {
        for (int i = 0; i < pairsnumber.Count; i++)
        {
            comparisonPairs[i].Init(pairsnumber[i].numberLeft, pairsnumber[i].numberRight);
        }
        
        StartCoroutine(DelayedRegisterEvents());
    }

    private IEnumerator DelayedRegisterEvents()
    {
        yield return null;
        yield return null;
        yield return null;
        
        bool allInitialized = false;
        while (!allInitialized)
        {
            allInitialized = true;
            foreach (var pair in comparisonPairs)
            {
                if (!pair.isDropdownInitialized)
                {
                    allInitialized = false;
                    break;
                }
            }
            
            if (!allInitialized)
            {
                yield return null;
            }
        }
        
        for (int i = 0; i < comparisonPairs.Count; i++)
        {
            var i1 = i;
            comparisonPairs[i].dropdown.onValueChanged.AddListener((value) => {
                if (value >= 0 && value < comparisonPairs[i1].dropdown.options.Count) {
                    OnDropdownValueChanged(comparisonPairs[i1], value);
                }
            });
        }
    }
    private void OnDropdownValueChanged(ComparisonPair pair, int value)
    {
        if(value < 0 || value >= pair.dropdown.options.Count)
            return;
        
        if (value == 0)
        {
            pair.dropdown.captionText.color = UnityEngine.Color.black;
            pair.isCorrect = false;
            return;
        }

        // Kiểm tra đáp án đúng (bây giờ các dấu so sánh bắt đầu từ index 1)
        if (pair.dropdown.options[value].text == pair.CorrectSign)
        {
            pair.dropdown.captionText.color = UnityEngine.Color.green;
            pair.isCorrect = true;
            onCorrectAnswer?.Invoke();
        }
        else
        {
            pair.dropdown.captionText.color = UnityEngine.Color.red;
            pair.isCorrect = false;
            _mistakeCount++;
            onWrongAnswer?.Invoke();
        }
        pair.dropdown.RefreshShownValue();
    }

    private void ResetComparisonPairs()
    {
        foreach (var pair in comparisonPairs)
        {   
            pair.dropdown.onValueChanged.RemoveAllListeners();
        }
    }

    private void InitializeExercise()
    {
        UIManager.Instance.ShowPanel(UIManager.PanelType.BlockPanel);
        AddEventListener();
        /*UIManager.Instance.ShowPanel(UIManager.PanelType.BlockPanel);*/
        TeacherCharacter.Instance.Speak(teacherToturialAudio, "Em hãy đi dấu thích hợp vào ô trống");
        Sequence(Delay(teacherToturialAudio.length + 1).OnComplete(() =>
        {
            MainManager.Instance.OnStartQuest?.Invoke();
            TimeManager.Instance.StartTiming();
        }));
        _mistakeCount = 0;
    }

    private void AddEventListener()
    {
        _hasInitialized = true;

        var teacher = TeacherCharacter.Instance;
        if (teacher != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(teacher.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(teacher.FalseReaction);
        }

        var boy = BoyCharacter.Instance;
        if (boy != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(boy.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(boy.FalseReaction);
        }

        var girl = GirlCharacter.Instance;
        if (girl != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.AddListener(girl.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.AddListener(girl.FalseReaction);
        }
    }

    private void CleanupEvents()
    {
        if (!_hasInitialized) return;

        if (completeButton != null)
        {
            completeButton.onClick.RemoveListener(OnCompleteButtonClicked);
        }

        RemoveEventListener();

        onExerciseCompleted?.RemoveAllListeners();
        onCorrectAnswer?.RemoveAllListeners();
        onWrongAnswer?.RemoveAllListeners();

        _hasInitialized = false;
    }

    private void RemoveEventListener()
    {
        var teacher = TeacherCharacter.Instance;
        if (teacher != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(teacher.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(teacher.FalseReaction);
        }

        var boy = BoyCharacter.Instance;
        if (boy != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(boy.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(boy.FalseReaction);
        }

        var girl = GirlCharacter.Instance;
        if (girl != null)
        {
            if (onCorrectAnswer != null) onCorrectAnswer.RemoveListener(girl.TrueReaction);
            if (onWrongAnswer != null) onWrongAnswer.RemoveListener(girl.FalseReaction);
        }
    }

    private void OnCompleteButtonClicked()
    {
        EvaluateExercise();
    }

    private void EvaluateExercise()
    {
        int errorCount = 0;
        foreach (var pair in comparisonPairs)
        {
            if (pair.isCorrect== false)
            {
                errorCount++;
            }
        }

        onExerciseCompleted?.Invoke();
        float timeTaken = TimeManager.Instance.StopTiming();
        /*SaveResultToDataManager(errorCount, score, timeTaken);*/

        /*string resultMessage = errorCount > 0
            ? $"Kết quả: {comparisonPairs.Count - errorCount}/{comparisonPairs.Count} số đúng. Số lần mắc lỗi: {_mistakeCount}. Điểm: {score:F1}/{maxScore:F1}"
            : $"Tuyệt vời! Bạn đã nhập đúng tất cả các số. Số lần mắc lỗi: {_mistakeCount}. Điểm: {score:F1}/{maxScore:F1}";

        Debug.Log(resultMessage);*/

        Deactivate();
        UIManager.Instance.ShowCompletePanel(comparisonPairs.Count - errorCount, errorCount, _mistakeCount, timeTaken);
        MainManager.Instance.isQuestStarted = false;
    }

}
