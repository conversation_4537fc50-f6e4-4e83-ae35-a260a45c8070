using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class ComparisonPair : Node
{
    [SerializeField]
    private TextMeshProUGUI numberLeftText;
    [SerializeField]
    private TextMeshProUGUI numberRightText;
    public TMP_Dropdown dropdown;

    public string CorrectSign { get; private set; }
    
    public bool isCorrect = false;
    private bool isInitialized = false;
    
    public bool isDropdownInitialized = false;

    protected override void OnEnable()
    {
        base.OnEnable();
        isCorrect = false;
        isInitialized = false;
        isDropdownInitialized = false;

        if (dropdown != null)
        {
            dropdown.onValueChanged.RemoveAllListeners();
            InitDropdown(); // Ensure dropdown options are always available
        }

        StartCoroutine(SafeResetDropdown());
        RepositionDropdown();
    }

    private void Start()
    {
        if (!isInitialized)
        {
            isCorrect = false;
            RepositionDropdown();
        }
    }

    public void Init(int numberLeft, int numberRight)
    {
        isInitialized = true;

        numberLeftText.text = numberLeft.ToString();
        numberRightText.text = numberRight.ToString();

        dropdown.onValueChanged.RemoveAllListeners();

        // Ensure dropdown has the correct options
        InitDropdown();

        if(numberLeft < numberRight)
        {
            CorrectSign = "<";
        }
        else if(numberLeft == numberRight)
        {
            CorrectSign = "=";
        }
        else
        {
            CorrectSign = ">";
        }

        // Reset dropdown to empty state after setting up options
        StartCoroutine(SafeResetDropdown());
    }
    
    private IEnumerator SafeResetDropdown()
    {
        yield return null;
        yield return null;

        // Đảm bảo dropdown có options trước khi reset
        if (dropdown.options.Count == 0)
        {
            InitDropdown();
        }

        // Thêm một option placeholder ở đầu để tránh conflict với index 0
        dropdown.ClearOptions();
        dropdown.AddOptions(new List<string>(){"...", "<", "=", ">"});

        dropdown.value = 0; // Set về placeholder
        dropdown.captionText.color = UnityEngine.Color.black;

        isDropdownInitialized = true;

        dropdown.interactable = true;
    }
    
    private void RepositionDropdown()
    {
        dropdown.transform.position = transform.position;
    }
    
    private void InitDropdown()
    {
        if (dropdown)
        {
            dropdown.ClearOptions();
            dropdown.AddOptions(new List<string>(){"...", "<", "=", ">"});
            Debug.Log($"Dropdown initialized with {dropdown.options.Count} options: {string.Join(", ", dropdown.options.ConvertAll(x => x.text))}");
        }
    }
}