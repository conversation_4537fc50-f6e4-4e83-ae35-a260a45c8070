using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class ComparisonPair : Node
{
    [SerializeField]
    private TextMeshProUGUI numberLeftText;
    [SerializeField]
    private TextMeshProUGUI numberRightText;
    public TMP_Dropdown dropdown;

    public string CorrectSign { get; private set; }
    
    public bool isCorrect = false;
    private bool isInitialized = false;
    
    public bool isDropdownInitialized = false;

    protected override void OnEnable()
    {
        base.OnEnable();
        isCorrect = false;
        isInitialized = false;
        isDropdownInitialized = false;
        
        if (dropdown != null)
        {
            dropdown.onValueChanged.RemoveAllListeners();
        }
        
        StartCoroutine(SafeResetDropdown());
        RepositionDropdown();
    }

    private void Start()
    {
        if (!isInitialized)
        {
            isCorrect = false;
            RepositionDropdown();
        }
    }

    public void Init(int numberLeft, int numberRight)
    {
        isInitialized = true;
        
        numberLeftText.text = numberLeft.ToString();
        numberRightText.text = numberRight.ToString();
        
        dropdown.onValueChanged.RemoveAllListeners();
        
        InitDropdown();
        
        if(numberLeft < numberRight)
        {
            CorrectSign = "<";
        }
        else if(numberLeft == numberRight)
        {
            CorrectSign = "=";
        }
        else
        {
            CorrectSign = ">";
        }
        
        StartCoroutine(SafeResetDropdown());
    }
    
    private IEnumerator SafeResetDropdown()
    {
        yield return null;
        yield return null;
        
        dropdown.value = -1;
        dropdown.captionText.color = UnityEngine.Color.black;
        dropdown.captionText.text = string.Empty;
        
        isDropdownInitialized = true;
        
        dropdown.interactable = true;
    }
    
    private void RepositionDropdown()
    {
        dropdown.transform.position = transform.position;
    }
    
    private void InitDropdown()
    {
        dropdown.ClearOptions();
        dropdown.AddOptions(new List<string>(){"<", "=", ">"});
    }
}